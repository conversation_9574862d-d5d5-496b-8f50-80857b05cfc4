#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI CMS Simplified - 企业内容管理系统精简版本

只保留核心功能：
1. Job/Booking数据导出
2. 人员/公司/部门名称查询
3. 系统监控
"""

import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager
import threading
import time

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, field_validator
import uvicorn
import pandas as pd
from pandasai import SmartDataframe, SmartDatalake
from pandasai.llm import AzureOpenAI
from pandasai.connectors import MySQLConnector, SQLConnector

# 导入数据库操作函数
from utils.database.db_pro2_basic import (
    search_company_by_part_name,
    search_user_by_part_name,
    search_department_by_part_name,
    get_sea_air_profit_with_transhipment,
    get_job_details_with_transhipment
)

from utils.basic.logger_config import setup_logger
from utils.basic.optimized_export import export_to_oss, apply_column_mapping
from utils.basic.data_cache_manager import (
    global_cache_manager,
    get_ai_analysis_data,
    set_ai_analysis_data
)
from utils.basic.data_conn_unified import (
    MYSQL_USER,
    MYSQL_PASSWORD,
    MYSQL_DB_MCP,
    get_ssh_manager
)

# 配置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

# 全局调度器变量
_profit_scheduler = None


# 使用全局缓存管理器，支持跨端点缓存共享

# PandasAI 配置
def get_pandasai_llm():
    """获取配置好的 PandasAI LLM 实例"""
    try:
        llm = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_base=os.getenv("AZURE_OPENAI_ENDPOINT"),
            api_version=os.getenv("AZURE_API_VERSION"),
            deployment_name=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME", "gpt-4.1-mini"),
        )
        return llm
    except Exception as e:
        logger.error(f"初始化 PandasAI LLM 失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"AI 分析服务初始化失败: {str(e)}"
        )


def get_mysql_config():
    """获取MySQL连接配置"""
    try:
        ssh_manager = get_ssh_manager()
        conn_params = ssh_manager.get_connection_params()

        if not isinstance(conn_params, dict):
            raise ValueError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")

        return {
            "host": conn_params['mysql_host'],
            "port": conn_params['mysql_port'],
            "database": MYSQL_DB_MCP,
            "username": MYSQL_USER,
            "password": MYSQL_PASSWORD,
        }
    except Exception as e:
        logger.error(f"获取MySQL配置失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"数据库连接配置失败: {str(e)}"
        )


async def analyze_data_with_pandasai(
    question: str,
    language: str = "zh",
    session_id: str = None,
    begin_date: str = None,
    end_date: str = None
) -> str:
    """使用 PandasAI 分析MySQL数据源中的数据"""
    try:
        # 获取MySQL连接配置
        mysql_config = get_mysql_config()
        logger.info(f"连接到MySQL数据库: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")

        # 获取 LLM 实例
        llm = get_pandasai_llm()

        # 构建where条件
        where_conditions = []
        if session_id:
            where_conditions.append(["session_id", "=", session_id])
        if begin_date:
            where_conditions.append(["job_date", ">=", begin_date])
        if end_date:
            where_conditions.append(["job_date", "<=", end_date])

        # 创建t_booking_details连接器
        booking_connector = SQLConnector(
            config={
                "dialect": "mysql",
                "driver": "pymysql",
                "host": mysql_config['host'],
                "port": mysql_config['port'],
                "database": mysql_config['database'],
                "username": mysql_config['username'],
                "password": mysql_config['password'],
                "table": "t_booking_details",
                "where": where_conditions if where_conditions else None,
            }
        )

        # 创建t_job_details连接器
        job_connector = SQLConnector(
            config={
                "dialect": "mysql",
                "driver": "pymysql",
                "host": mysql_config['host'],
                "port": mysql_config['port'],
                "database": mysql_config['database'],
                "username": mysql_config['username'],
                "password": mysql_config['password'],
                "table": "t_job_details",
                "where": where_conditions if where_conditions else None,
            }
        )

        # 创建 SmartDatalake 来处理多个表
        lake = SmartDatalake(
            [booking_connector, job_connector],
            config={
                "llm": llm,
                "enable_cache": False,
                "verbose": True,
                "enforce_privacy": True,
                "save_charts": False,
            },
        )

        logger.info(f"成功创建SmartDatalake，包含t_booking_details和t_job_details表")

        # 根据语言调整问题
        if language == "zh":
            analysis_prompt = f"""
            请用中文回答以下问题：{question}

            数据说明：
            - t_booking_details表包含订舱数据详情
            - t_job_details表包含工作档数据详情
            - 两个表的job_id字段含义相同，可以关联使用
            - 如果存在相同的job_id或job_id+bkbl_id的记录，请选择id字段值更大的记录作为最终数据

            请提供详细的分析结果，包括：
            1. 数据概览
            2. 关键发现
            3. 趋势分析
            4. 建议或结论

            请确保回答简洁明了，重点突出。
            """
        else:
            analysis_prompt = f"""
            Please answer the following question in English: {question}

            Data Description:
            - t_booking_details table contains booking data details
            - t_job_details table contains job data details
            - The job_id field in both tables has the same meaning and can be used for correlation
            - If there are records with the same job_id or job_id+bkbl_id, please select the record with the larger id field value as the final data

            Please provide detailed analysis results including:
            1. Data overview
            2. Key findings
            3. Trend analysis
            4. Recommendations or conclusions

            Please ensure the answer is concise and highlights key points.
            """

        # 执行分析
        logger.info(f"开始执行 AI 分析: {question}")
        result = lake.chat(analysis_prompt)

        if result is None or str(result).strip() == "":
            return "AI 分析未能生成有效结果。请尝试重新表述您的问题。"

        logger.info("AI 分析完成")
        return str(result)

    except Exception as e:
        logger.error(f"PandasAI 分析失败: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")

        if "API" in str(e) or "authentication" in str(e).lower():
            return f"AI 服务连接失败: {str(e)}。请检查 API 配置。"
        elif "timeout" in str(e).lower():
            return "AI 分析超时。请尝试简化问题或缩小数据范围。"
        else:
            return f"AI 分析过程中出现错误: {str(e)}"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan handler that manages profit data scheduler startup and shutdown."""
    global _profit_scheduler

    # Startup
    # 检查是否启用自动提取功能
    enable_auto_extract = os.getenv('ENABLE_AUTO_EXTRACT', 'False').lower() == 'true'

    if enable_auto_extract:
        try:
            # 导入调度器类（延迟导入避免循环依赖）
            from ..basic.profit_data_scheduler import ProfitDataScheduler

            _profit_scheduler = ProfitDataScheduler()
            logger.info("正在启动利润数据周期性调度器...")

            # 在后台启动调度器
            _profit_scheduler.current_task = asyncio.create_task(_profit_scheduler.start_scheduler())

            logger.info("✅ 利润数据调度器已启动（后台运行）")
            logger.info("📋 调度规则：")
            logger.info("   • 距今2年以上：每3个月检查一次")
            logger.info("   • 距今1-2年：每3个月检查一次")
            logger.info("   • 距今3个月-1年：每1个月检查一次")
            logger.info("   • 距今1-3个月：每天检查一次")
            logger.info("   • 3个月数据周期，自动增量更新")
            logger.info("   • 执行时间：每日20:00至次日8:00")

        except Exception as e:
            logger.error(f"❌ 启动利润数据调度器失败: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
    else:
        logger.info("⏸️ 自动提取功能已禁用")
        logger.info("💡 往期job和booking业务数据自动提取功能未启用")
        logger.info("   如需启用，请使用 --enable-auto-extract 参数重启服务器")
    
    yield
    
    # Shutdown
    if _profit_scheduler:
        try:
            logger.info("正在关闭利润数据调度器...")
            _profit_scheduler.is_running = False
            if _profit_scheduler.current_task:
                _profit_scheduler.current_task.cancel()
                try:
                    await _profit_scheduler.current_task
                except asyncio.CancelledError:
                    pass
            logger.info("✅ 利润数据调度器已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭利润数据调度器失败: {e}")

app = FastAPI(
    title="CMS Simplified",
    description="企业内容管理系统精简版本 - 专注数据导出",
    version="4.0.0",
    lifespan=lifespan
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型定义
class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., min_length=1, description="搜索关键词")
    search_type: str = Field(default="company", description="搜索类型")
    
    @field_validator('search_type')
    @classmethod
    def validate_search_type(cls, v):
        if v not in ['company', 'user', 'department', 'salesman']:
            raise ValueError('搜索类型必须为 company, user, department 或 salesman')
        return v

class DateRangeRequest(BaseModel):
    """日期范围请求模型"""
    begin_date: str = Field(..., description="开始日期 (YYYY-MM-DD)")
    end_date: str = Field(..., description="结束日期 (YYYY-MM-DD)")
    format: str = Field(default="excel", description="导出格式")

    @field_validator('begin_date', 'end_date')
    @classmethod
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('日期格式必须为 YYYY-MM-DD')

    @field_validator('format')
    @classmethod
    def validate_format(cls, v):
        if v.lower() not in ['excel', 'csv']:
            raise ValueError('导出格式必须为 excel 或 csv')
        return v.lower()


class DataAnalysisRequest(BaseModel):
    """数据分析请求模型"""
    begin_date: str = Field(..., description="开始日期 (YYYY-MM-DD)")
    end_date: str = Field(..., description="结束日期 (YYYY-MM-DD)")
    data_type: str = Field(..., description="数据类型: booking 或 job")
    question: str = Field(..., min_length=1, description="分析问题（自然语言）")
    language: str = Field(default="zh", description="返回语言: zh 或 en")

    @field_validator('begin_date', 'end_date')
    @classmethod
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('日期格式必须为 YYYY-MM-DD')

    @field_validator('data_type')
    @classmethod
    def validate_data_type(cls, v):
        if v.lower() not in ['booking', 'job']:
            raise ValueError('数据类型必须为 booking 或 job')
        return v.lower()

    @field_validator('language')
    @classmethod
    def validate_language(cls, v):
        if v.lower() not in ['zh', 'en']:
            raise ValueError('语言必须为 zh 或 en')
        return v.lower()


class SystemStatus(BaseModel):
    """系统状态模型"""
    status: str
    timestamp: datetime
    database_connection: bool
    api_version: str
    feature_count: int


# 健康检查
@app.get("/health", response_model=SystemStatus)
async def health_check():
    """System health check endpoint that verifies database connectivity, API status, and core service availability. Returns comprehensive system status including connection status and feature availability."""
    try:
        await search_company_by_part_name("__test_connection__")
        db_status = True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        db_status = False
    
    return SystemStatus(
        status="healthy" if db_status else "degraded",
        timestamp=datetime.now(),
        database_connection=db_status,
        api_version="4.0.0",
        feature_count=3  # 名称查询、数据导出、健康检查
    )

# 统一搜索
@app.post("/search/", response_model=Dict[str, Any])
async def unified_search(request: SearchRequest):
    """Unified search service for finding companies, users, departments, and salespeople by partial name matching. Supports fuzzy search across all entity types in the logistics management system."""
    logger.info(f"统一搜索请求: 类型={request.search_type}, 关键词={request.query}")
    
    try:
        start_time = datetime.now()
        
        if request.search_type == "company":
            results = await search_company_by_part_name(request.query)
        elif request.search_type == "user":
            results = await search_user_by_part_name(request.query)
        elif request.search_type == "salesman":
            # 业务员搜索映射到用户搜索
            results = await search_user_by_part_name(request.query)
        elif request.search_type == "department":
            results = await search_department_by_part_name(request.query)
        else:
            raise HTTPException(status_code=400, detail="不支持的搜索类型")
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return {
            "success": True,
            "search_type": request.search_type,
            "query": request.query,
            "data": results or [],
            "count": len(results) if results else 0,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"统一搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

# 导出Booking数据
@app.post("/export/bookings", response_model=Dict[str, Any])
async def export_bookings(request: DateRangeRequest):
    """Export shipping booking data with profit analysis including revenue, costs, and carrier details for specified date range. Returns downloadable Excel/CSV file with TEU calculations and financial metrics."""
    try:
        logger.info(f"开始导出Booking数据: {request.begin_date} 至 {request.end_date}")
        start_time = datetime.now()
        
        try:
            data = await asyncio.wait_for(
                get_sea_air_profit_with_transhipment(
                    request.begin_date, 
                    request.end_date
                ),
                timeout=600.0
            )
        except asyncio.TimeoutError:
            logger.error(f"导出Booking数据超时: {request.begin_date} 至 {request.end_date}")
            raise HTTPException(
                status_code=408,
                detail="导出Booking数据超时，请缩小日期范围或联系管理员"
            )
        
        filename_prefix = f"bookings_{request.begin_date}_{request.end_date}"
        download_url = await export_to_oss(
            data, 
            filename_prefix, 
            data_type="booking",
            file_format=request.format
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 获取记录数
        record_count = data.get('total_count', 0) if isinstance(data, dict) else len(data)
        
        return {
            "success": True,
            "message": "Booking数据导出成功",
            "download_url": download_url,
            "record_count": record_count,
            "date_range": f"{request.begin_date} 至 {request.end_date}",
            "format": request.format,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"导出Booking数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出Booking数据失败: {str(e)}")

# 导出Job数据
@app.post("/export/jobs", response_model=Dict[str, Any])
async def export_jobs(request: DateRangeRequest):
    """Export job order data with detailed logistics operations including container tracking, consolidation details, approval workflows, and business metrics for specified date range. Returns downloadable Excel/CSV file."""
    try:
        logger.info(f"开始导出Job数据: {request.begin_date} 至 {request.end_date}")
        start_time = datetime.now()
        
        try:
            data = await asyncio.wait_for(
                get_job_details_with_transhipment(
                    request.begin_date, 
                    request.end_date
                ),
                timeout=600.0
            )
        except asyncio.TimeoutError:
            logger.error(f"导出Job数据超时: {request.begin_date} 至 {request.end_date}")
            raise HTTPException(
                status_code=408,
                detail="导出Job数据超时，请缩小日期范围或联系管理员"
            )
        
        filename_prefix = f"jobs_{request.begin_date}_{request.end_date}"
        download_url = await export_to_oss(
            data, 
            filename_prefix, 
            data_type="job",
            file_format=request.format
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 获取记录数并检查数据是否为空
        record_count = data.get('total_count', 0) if isinstance(data, dict) else len(data)
        actual_data = data.get('data', []) if isinstance(data, dict) else data
        
        if not actual_data or len(actual_data) == 0:
            logger.warning(f"Job数据导出结果为空: {request.begin_date} 至 {request.end_date}")
            return {
                "success": False,
                "message": "指定日期范围内没有找到Job数据，请检查日期范围或数据库连接",
                "download_url": None,
                "record_count": 0,
                "date_range": f"{request.begin_date} 至 {request.end_date}",
                "format": request.format,
                "execution_time_seconds": execution_time,
                "timestamp": datetime.now().isoformat(),
                "suggestion": "请尝试扩大日期范围或检查数据库连接配置"
            }
        
        return {
            "success": True,
            "message": "Job数据导出成功",
            "download_url": download_url,
            "record_count": record_count,
            "date_range": f"{request.begin_date} 至 {request.end_date}",
            "format": request.format,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"导出Job数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出Job数据失败: {str(e)}")


# 数据分析端点
@app.post("/analyze/data", response_model=Dict[str, Any])
async def analyze_data(request: DataAnalysisRequest):
    """AI-powered data analysis service using PandasAI for natural language queries on business data. Supports both booking and job data analysis with intelligent insights, trend analysis, and recommendations in Chinese or English. Features intelligent caching for improved performance in continuous queries."""
    try:
        logger.info(f"开始数据分析: 类型={request.data_type}, 日期={request.begin_date} 至 {request.end_date}, 问题={request.question}")
        start_time = datetime.now()

        logger.info(f"开始AI分析，直接连接MySQL数据库")

        # 执行AI分析
        try:
            analysis_result = await asyncio.wait_for(
                analyze_data_with_pandasai(
                    question=request.question,
                    language=request.language,
                    session_id=None,  # 可以根据需要添加session_id逻辑
                    begin_date=request.begin_date,
                    end_date=request.end_date
                ),
                timeout=600.0  # 10分钟超时
            )
        except asyncio.TimeoutError:
            logger.error("AI分析超时")
            raise HTTPException(
                status_code=408,
                detail="AI分析超时，请尝试简化问题或缩小数据范围"
            )

        execution_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": True,
            "message": "数据分析完成",
            "analysis_result": analysis_result,
            "question": request.question,
            "language": request.language,
            "data_info": {
                "data_type": request.data_type,
                "date_range": f"{request.begin_date} 至 {request.end_date}",
                "data_source": "MySQL数据库直连",
                "tables": ["t_booking_details", "t_job_details"]
            },
            "performance_info": {
                "total_execution_time_seconds": execution_time,
                "data_source": "MySQL直连（t_booking_details + t_job_details）",
                "deduplication_logic": "相同job_id或job_id+bkbl_id时选择id更大的记录"
            },
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"数据分析失败: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"数据分析失败: {str(e)}")


# 缓存管理端点
@app.get("/cache/stats", response_model=Dict[str, Any])
async def get_cache_stats():
    """Get data cache statistics and performance metrics for monitoring cache efficiency and hit rates across all endpoints."""
    try:
        stats = global_cache_manager.get_stats()
        return {
            "success": True,
            "message": "全局缓存统计信息获取成功",
            "cache_stats": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")


@app.post("/cache/clear", response_model=Dict[str, Any])
async def clear_cache():
    """Clear all cached data to free memory and force fresh data retrieval for subsequent requests across all endpoints."""
    try:
        old_stats = global_cache_manager.get_stats()
        global_cache_manager.clear()

        return {
            "success": True,
            "message": "全局缓存已清空",
            "cleared_items": old_stats['cache_size'],
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"清空缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
    