#!/usr/bin/env python3
"""
利润数据周期性调度器
负责自动、周期性地运行 job_details 和 booking_details 数据分析并写入数据库
"""

import asyncio
import logging
import hashlib
import uuid
import os
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from decimal import Decimal
import aiomysql
import json

from .logger_config import setup_logger
from .data_conn_unified import get_mysql_connection, MYSQL_DB_MCP
from ..database.db_pro2_basic import get_job_details_with_transhipment, get_sea_air_profit_with_transhipment
# 替换nan_value_cleaner为简单的内联函数
import pandas as pd

def is_nan_value(value) -> bool:
    """检查值是否为NaN"""
    if value is None:
        return False
    if pd.isna(value):
        return True
    if isinstance(value, str) and value.lower().strip() == 'nan':
        return True
    return False

logger = setup_logger(__name__)

@dataclass
class AnalysisPeriod:
    """分析周期配置"""
    start_date: date
    end_date: date
    period_type: str  # "2years+", "1-2years", "4months-1year", "current"
    check_frequency: str  # "3months", "1month", "1week"
    last_check: Optional[datetime] = None
    
class ProfitDataScheduler:
    """利润数据调度器"""
    
    def __init__(self):
        self.is_running = False
        self.current_task = None
        # 从环境变量获取系统ID
        self.pro2_system_id = int(os.getenv('PRO2_SYSTEM_ID', 0))
        if self.pro2_system_id == 0:
            logger.warning("PRO2_SYSTEM_ID环境变量未设置或为0，请检查配置")
            raise ValueError("PRO2_SYSTEM_ID环境变量必须设置且不能为0")
        logger.info(f"当前系统ID: {self.pro2_system_id}")
        
    async def ensure_required_tables_exist(self):
        """确保所有必需的表存在 - 优化版本，先检查表是否存在"""
        try:
            async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
                async with connection.cursor() as cursor:
                    # 检查所有必需的表是否存在
                    await cursor.execute("""
                        SELECT COUNT(*) as table_count 
                        FROM information_schema.tables 
                        WHERE table_schema = %s 
                        AND table_name IN ('t_scheduler_check_log', 't_job_details', 't_booking_details')
                    """, (MYSQL_DB_MCP,))
                    
                    result = await cursor.fetchone()
                    if result and result[0] == 3:  # 所有表都存在
                        logger.debug("所有必需的表已存在，跳过创建")
                        return
                    
                    logger.info("检测到缺少表，开始创建数据库表...")
                    # 创建调度器检查日志表
                    await cursor.execute("""
                        CREATE TABLE IF NOT EXISTS `t_scheduler_check_log` (
                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                          `pro2_system_id` int NOT NULL COMMENT '系统ID，区分不同地区/服务器',
                          `period_start` date NOT NULL COMMENT '分析周期开始日期',
                          `period_end` date NOT NULL COMMENT '分析周期结束日期',
                          `period_type` varchar(20) NOT NULL COMMENT '周期类型：2years+, 1-2years, 6months-1year, 1-5months',
                          `check_frequency` varchar(10) NOT NULL COMMENT '检查频率：6months, 3months, 1month, 1week',
                          `last_check_time` datetime NOT NULL COMMENT '最后检查时间',
                          `job_data_hash` varchar(32) DEFAULT NULL COMMENT 'Job数据哈希值',
                          `booking_data_hash` varchar(32) DEFAULT NULL COMMENT 'Booking数据哈希值',
                          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                          PRIMARY KEY (`id`),
                          UNIQUE KEY `uk_period` (`pro2_system_id`, `period_start`, `period_end`, `period_type`),
                          KEY `idx_system_check_time` (`pro2_system_id`, `last_check_time`),
                          KEY `idx_period_type` (`period_type`, `check_frequency`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='调度器检查日志表'
                    """)
                    
                    # 创建Job详情数据表
                    await cursor.execute("""
                        CREATE TABLE IF NOT EXISTS `t_job_details` (
                          `id` int NOT NULL AUTO_INCREMENT,
                          `session_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID，用于数据隔离',
                          `analysis_timestamp` bigint NOT NULL COMMENT '分析时间戳',
                          `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据插入时间',
                          `job_type_cn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务类型中文名 (海运出口/海运进口/海运三角/空运出口/空运进口)',
                          `job_date` date DEFAULT NULL COMMENT '工作档日期 (YYYY-MM-DD)',
                          `job_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作档编号',
                          `vessel` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '船名/航班号',
                          `voyage` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '航次/航班信息',
                          `pol_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '起运港代码',
                          `pod_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '卸货港代码',
                          `bk_count` int DEFAULT NULL COMMENT '订舱数量',
                          `cbm` decimal(10,3) DEFAULT NULL COMMENT '总体积',
                          `rt` decimal(10,3) DEFAULT NULL COMMENT '总计费数量',
                          `teu` decimal(10,3) DEFAULT NULL COMMENT '总TEU',
                          `income` decimal(15,2) DEFAULT NULL COMMENT '收入',
                          `cost` decimal(15,2) DEFAULT NULL COMMENT '成本',
                          `profit` decimal(15,2) DEFAULT NULL COMMENT '毛利润',
                          `operator_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员名称',
                          `job_handling_agent` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作档代理名称',
                          `nomi_count` int DEFAULT NULL COMMENT '指定货票数',
                          `nomi_rt` decimal(10,3) DEFAULT NULL COMMENT '指定货rt总和',
                          `is_consolidation` tinyint DEFAULT NULL COMMENT '是否集拼',
                          `bill_count` int DEFAULT NULL COMMENT '提单数量',
                          `consolidation_20` int DEFAULT NULL COMMENT '集拼20尺柜数量',
                          `consolidation_40` int DEFAULT NULL COMMENT '集拼40尺柜数量',
                          `operator_department` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员部门名称',
                          `is_op_finished` tinyint DEFAULT NULL COMMENT '操作是否完成',
                          `is_checked` tinyint DEFAULT NULL COMMENT '是否已审核',
                          `job_id` int DEFAULT NULL COMMENT '工作档ID',
                          `job_type_id` int DEFAULT NULL COMMENT '业务类型ID',
                          `operator_id` int DEFAULT NULL COMMENT '操作员ID',
                          `etd_date` date DEFAULT NULL COMMENT '预计离港日期',
                          `eta_date` date DEFAULT NULL COMMENT '预计到港日期',
                          `pro2_system_id` int DEFAULT NULL,
                          `data_hash` varchar(32) DEFAULT NULL COMMENT '业务数据哈希值，用于变更检测',
                          PRIMARY KEY (`id`),
                          KEY `idx_session` (`session_id`),
                          KEY `idx_timestamp` (`analysis_timestamp`),
                          KEY `idx_created` (`created_at`),
                          KEY `idx_session_date` (`session_id`,`job_date`),
                          KEY `idx_session_type` (`session_id`,`job_type_cn`),
                          KEY `idx_session_operator` (`session_id`,`operator_name`),
                          KEY `idx_session_port` (`session_id`,`pol_code`,`pod_code`),
                          KEY `idx_session_profit` (`session_id`,`profit`),
                          KEY `idx_session_dept` (`session_id`,`operator_department`),
                          KEY `idx_session_vessel` (`session_id`,`vessel`),
                          KEY `idx_session_consolidation` (`session_id`,`is_consolidation`),
                          KEY `idx_session_containers` (`session_id`,`consolidation_20`,`consolidation_40`),
                          KEY `idx_session_revenue` (`session_id`,`income`),
                          KEY `idx_session_cost` (`session_id`,`cost`),
                          KEY `idx_data_hash` (`data_hash`),
                          KEY `idx_job_no_hash` (`job_no`,`data_hash`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作档数据分析固定表 - 替代临时表方案'
                    """)
                    
                    # 创建Booking详情数据表
                    await cursor.execute("""
                        CREATE TABLE IF NOT EXISTS `t_booking_details` (
                          `id` int NOT NULL AUTO_INCREMENT,
                          `session_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID，用于数据隔离',
                          `analysis_timestamp` bigint NOT NULL COMMENT '分析时间戳',
                          `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据插入时间',
                          `job_type_cn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务类型中文名 (海运出口/海运进口/海运三角/空运出口/空运进口)',
                          `job_date` date DEFAULT NULL COMMENT '工作档日期 (YYYY-MM-DD)',
                          `job_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作档编号',
                          `bkbl_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订舱/提单编号',
                          `client_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户名称',
                          `vessel` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '船名/航班号',
                          `voyage` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '航次/航班日期',
                          `job_pol` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '航次始发港',
                          `bill_pol` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '提单起运港',
                          `bill_pod` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '提单卸货港',
                          `service_mode` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '服务模式 (LCL/FCL/BUY-CONSOL/AIR)',
                          `lcl_rt` decimal(10,3) DEFAULT NULL COMMENT '拼箱计费重量 (服务模式为LCL时有值)',
                          `teu` decimal(10,3) DEFAULT NULL COMMENT 'TEU数量 (服务模式为FCL时=计费数量/1000)',
                          `air_weight` decimal(10,3) DEFAULT NULL COMMENT '空运计费重量 (服务模式为AIR时有值)',
                          `income` decimal(15,2) DEFAULT NULL COMMENT '收入',
                          `cost` decimal(15,2) DEFAULT NULL COMMENT '成本',
                          `profit` decimal(15,2) DEFAULT NULL COMMENT '利润',
                          `transhipment_profit` decimal(15,2) DEFAULT NULL COMMENT '转运利润',
                          `total_business_profit` decimal(15,2) DEFAULT NULL COMMENT '业务总利润 (利润+转运利润)',
                          `is_freehand` tinyint DEFAULT NULL COMMENT '是否自揽货 (0/1)',
                          `salesman_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务员',
                          `salesman_department` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务员部门名称',
                          `operator_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员名称',
                          `operator_department` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员部门名称',
                          `coloader_name` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Coloader名称',
                          `job_handling_agent` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作档代理',
                          `bl_handling_agent` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作代理',
                          `is_transhipment` tinyint DEFAULT NULL COMMENT '是否转运 (0/1)',
                          `transhipment_id` int DEFAULT NULL COMMENT '转运ID',
                          `bkbl_id` int DEFAULT NULL COMMENT '订舱/提单ID',
                          `job_id` int DEFAULT NULL COMMENT '工作档ID',
                          `job_type_id` int DEFAULT NULL COMMENT '业务类型ID',
                          `operator_id` int DEFAULT NULL COMMENT '操作员ID',
                          `pro2_system_id` int DEFAULT NULL,
                          `data_hash` varchar(32) DEFAULT NULL COMMENT '业务数据哈希值，用于变更检测',
                          PRIMARY KEY (`id`),
                          KEY `idx_session` (`session_id`),
                          KEY `idx_timestamp` (`analysis_timestamp`),
                          KEY `idx_created` (`created_at`),
                          KEY `idx_session_date` (`session_id`,`job_date`),
                          KEY `idx_session_type` (`session_id`,`job_type_cn`),
                          KEY `idx_session_service` (`session_id`,`service_mode`),
                          KEY `idx_session_salesman` (`session_id`,`salesman_name`),
                          KEY `idx_session_operator` (`session_id`,`operator_name`),
                          KEY `idx_session_client` (`session_id`,`client_name`),
                          KEY `idx_session_profit` (`session_id`,`profit`),
                          KEY `idx_session_coloader` (`session_id`,`coloader_name`),
                          KEY `idx_session_dept` (`session_id`,`operator_department`),
                          KEY `idx_session_revenue` (`session_id`,`income`),
                          KEY `idx_session_cost` (`session_id`,`cost`),
                          KEY `idx_session_ports` (`session_id`,`bill_pol`,`bill_pod`),
                          KEY `idx_data_hash` (`data_hash`),
                          KEY `idx_job_no_hash` (`job_no`,`data_hash`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订舱数据分析固定表 - 替代临时表方案'
                    """)
                    
                    # 修改现有表的session_id字段长度（如果需要）
                    try:
                        await cursor.execute("""
                            ALTER TABLE `t_job_details` 
                            MODIFY COLUMN `session_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID，用于数据隔离'
                        """)
                        logger.info("✅ t_job_details表session_id字段已更新为64字符")
                    except Exception as e:
                        logger.debug(f"修改t_job_details表session_id字段: {e}")
                    
                    try:
                        await cursor.execute("""
                            ALTER TABLE `t_booking_details` 
                            MODIFY COLUMN `session_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID，用于数据隔离'
                        """)
                        logger.info("✅ t_booking_details表session_id字段已更新为64字符")
                    except Exception as e:
                        logger.debug(f"修改t_booking_details表session_id字段: {e}")
                    
                    # 添加数据哈希字段和索引（如果不存在）
                    try:
                        await cursor.execute("""
                            ALTER TABLE `t_job_details` 
                            ADD COLUMN `data_hash` varchar(32) DEFAULT NULL COMMENT '业务数据哈希值，用于变更检测'
                        """)
                        logger.info("✅ t_job_details表已添加data_hash字段")
                    except Exception as e:
                        logger.debug(f"添加t_job_details表data_hash字段: {e}")
                    
                    try:
                        await cursor.execute("""
                            ALTER TABLE `t_booking_details` 
                            ADD COLUMN `data_hash` varchar(32) DEFAULT NULL COMMENT '业务数据哈希值，用于变更检测'
                        """)
                        logger.info("✅ t_booking_details表已添加data_hash字段")
                    except Exception as e:
                        logger.debug(f"添加t_booking_details表data_hash字段: {e}")
                    
                    # 添加缺失的转运利润字段到t_job_details表
                    try:
                        await cursor.execute("""
                            ALTER TABLE `t_job_details`
                            ADD COLUMN `transhipment_profit` decimal(15,2) DEFAULT NULL COMMENT '转运利润'
                        """)
                        logger.info("✅ t_job_details表已添加transhipment_profit字段")
                    except Exception as e:
                        logger.debug(f"添加t_job_details表transhipment_profit字段: {e}")

                    try:
                        await cursor.execute("""
                            ALTER TABLE `t_job_details`
                            ADD COLUMN `total_business_profit` decimal(15,2) DEFAULT NULL COMMENT '业务总利润 (利润+转运利润)'
                        """)
                        logger.info("✅ t_job_details表已添加total_business_profit字段")
                    except Exception as e:
                        logger.debug(f"添加t_job_details表total_business_profit字段: {e}")

                    # 添加缺失的salesman_id字段到t_booking_details表
                    try:
                        await cursor.execute("""
                            ALTER TABLE `t_booking_details`
                            ADD COLUMN `salesman_id` int DEFAULT NULL COMMENT '业务员ID'
                        """)
                        logger.info("✅ t_booking_details表已添加salesman_id字段")
                    except Exception as e:
                        logger.debug(f"添加t_booking_details表salesman_id字段: {e}")

                    # 添加哈希相关索引
                    try:
                        await cursor.execute("ALTER TABLE `t_job_details` ADD INDEX `idx_data_hash` (`data_hash`)")
                        await cursor.execute("ALTER TABLE `t_job_details` ADD INDEX `idx_job_no_hash` (`job_no`,`data_hash`)")
                        logger.info("✅ t_job_details表哈希索引已添加")
                    except Exception as e:
                        logger.debug(f"添加t_job_details表哈希索引: {e}")

                    try:
                        await cursor.execute("ALTER TABLE `t_booking_details` ADD INDEX `idx_data_hash` (`data_hash`)")
                        await cursor.execute("ALTER TABLE `t_booking_details` ADD INDEX `idx_job_no_hash` (`job_no`,`data_hash`)")
                        logger.info("✅ t_booking_details表哈希索引已添加")
                    except Exception as e:
                        logger.debug(f"添加t_booking_details表哈希索引: {e}")
                    
                    await connection.commit()
                    logger.info("所有必需的表已确保存在，字段长度已更新")
        except Exception as e:
            logger.error(f"创建必需表失败: {e}")
            raise
        
    async def should_run_analysis(self, current_dt: datetime = None, period_type: str = None) -> bool:
        """
        检查是否应该运行分析 - 使用统一的执行时间配置
        测试阶段时间安排：
        - 周一至周五：20:00-24:00 和 06:00-09:00
        - 周六和周日：07:00-24:00
        """
        if current_dt is None:
            current_dt = datetime.now()

        # 导入Config类（使用绝对导入避免相对导入问题）
        try:
            from config import Config
        except ImportError:
            # 如果绝对导入失败，尝试相对导入
            from ...config import Config

        # 使用统一的执行时间检查
        return Config.is_execution_time(current_dt)

    def _get_month_start(self, year: int, month: int) -> date:
        """获取指定年月的第一天"""
        return date(year, month, 1)

    def _get_month_end(self, year: int, month: int) -> date:
        """获取指定年月的最后一天"""
        if month == 12:
            next_month_start = date(year + 1, 1, 1)
        else:
            next_month_start = date(year, month + 1, 1)
        return next_month_start - timedelta(days=1)

    def _add_months(self, start_date: date, months: int) -> date:
        """给日期添加指定月数，返回月份的第一天"""
        year = start_date.year
        month = start_date.month + months

        while month > 12:
            year += 1
            month -= 12
        while month < 1:
            year -= 1
            month += 12

        return self._get_month_start(year, month)

    def _get_quarter_info(self, date_obj: date) -> tuple:
        """获取日期所属的季度信息 (year, quarter)"""
        quarter = (date_obj.month - 1) // 3 + 1
        return date_obj.year, quarter

    def _get_quarter_start(self, year: int, quarter: int) -> date:
        """获取指定年份季度的开始日期"""
        month = (quarter - 1) * 3 + 1
        return date(year, month, 1)

    def _get_quarter_end(self, year: int, quarter: int) -> date:
        """获取指定年份季度的结束日期"""
        month = quarter * 3
        return self._get_month_end(year, month)

    def _add_quarters(self, year: int, quarter: int, quarters: int) -> tuple:
        """给季度添加指定数量的季度，返回(year, quarter)"""
        total_quarters = (year - 1) * 4 + quarter + quarters
        new_year = (total_quarters - 1) // 4 + 1
        new_quarter = (total_quarters - 1) % 4 + 1
        return new_year, new_quarter

    def get_analysis_periods(self, reference_date: date = None) -> List[AnalysisPeriod]:
        """
        获取需要分析的时间周期 - 按月份划分
        根据用户要求：
        - 分析数据从2020年1月开始
        - 如果当前是每月8日及以后：分析到上个月为止
        - 如果当前是1-7日：分析到上上个月为止
        - 例如：2025年5月6日 → 分析2020年1月至2025年3月
        - 例如：2025年5月8日 → 分析2020年1月至2025年4月
        """
        if reference_date is None:
            reference_date = date.today()

        periods = []
        
        # 确定截止月份
        current_year = reference_date.year
        current_month = reference_date.month
        
        if reference_date.day <= 7:
            # 1-7日：分析到上上个月
            if current_month <= 2:
                end_year = current_year - 1
                end_month = current_month + 10  # 上上个月
            else:
                end_year = current_year
                end_month = current_month - 2
        else:
            # 8日及以后：分析到上个月
            if current_month == 1:
                end_year = current_year - 1
                end_month = 12
            else:
                end_year = current_year
                end_month = current_month - 1
        
        logger.info(f"数据分析时间范围：2020年1月 至 {end_year}年{end_month}月")
        
        # 生成从2020年1月到截止月份的所有月份
        start_year, start_month = 2020, 1
        
        year, month = start_year, start_month
        while (year, month) <= (end_year, end_month):
            # 计算月份开始和结束日期
            month_start = date(year, month, 1)
            if month == 12:
                month_end = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                month_end = date(year, month + 1, 1) - timedelta(days=1)
            
            # 根据数据年龄确定检查频率和周期类型
            months_ago = (current_year - year) * 12 + (current_month - month)
            
            if months_ago >= 24:  # 2年以上
                period_type = "2years+"
                check_frequency = "3months"
            elif months_ago >= 12:  # 1-2年
                period_type = "1-2years" 
                check_frequency = "3months"
            elif months_ago >= 3:  # 3个月-1年
                period_type = "3months-1year"
                check_frequency = "1month"
            else:  # 1-3个月
                period_type = "1-3months"
                check_frequency = "1week"
            
            periods.append(AnalysisPeriod(
                start_date=month_start,
                end_date=month_end,
                period_type=period_type,
                check_frequency=check_frequency
            ))
            
            # 移动到下一个月
            if month == 12:
                year, month = year + 1, 1
            else:
                month += 1
        
        logger.info(f"生成了 {len(periods)} 个月度分析周期")
        return periods

    def _overlaps_excluded_months(self, start_date: date, end_date: date, excluded_months: List[date]) -> bool:
        """检查时间段是否与排除的月份重叠"""
        for excluded_month in excluded_months:
            # 获取排除月份的开始和结束日期
            month_start = excluded_month
            if excluded_month.month == 12:
                month_end = excluded_month.replace(year=excluded_month.year + 1, month=1) - timedelta(days=1)
            else:
                month_end = excluded_month.replace(month=excluded_month.month + 1) - timedelta(days=1)
                
            # 检查是否有重叠
            if not (end_date < month_start or start_date > month_end):
                return True
        return False
        
    async def get_last_check_time(self, period: AnalysisPeriod) -> Optional[datetime]:
        """从数据库获取周期的最后检查时间"""
        try:
            async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute("""
                        SELECT last_check_time FROM t_scheduler_check_log 
                        WHERE pro2_system_id = %s 
                        AND period_start = %s 
                        AND period_end = %s 
                        AND period_type = %s
                        ORDER BY last_check_time DESC 
                        LIMIT 1
                    """, (self.pro2_system_id, period.start_date, period.end_date, period.period_type))
                    
                    result = await cursor.fetchone()
                    return result['last_check_time'] if result else None
                    
        except Exception as e:
            logger.error(f"获取最后检查时间失败: {e}")
            return None
    
    async def update_last_check_time(self, period: AnalysisPeriod, check_time: datetime, job_hash: str = None, booking_hash: str = None):
        """更新周期的最后检查时间和数据哈希值到数据库"""
        try:
            async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
                async with connection.cursor() as cursor:
                    await cursor.execute("""
                        INSERT INTO t_scheduler_check_log 
                        (pro2_system_id, period_start, period_end, period_type, check_frequency, 
                         last_check_time, job_data_hash, booking_data_hash, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW()) AS new_values
                        ON DUPLICATE KEY UPDATE 
                        last_check_time = new_values.last_check_time,
                        job_data_hash = new_values.job_data_hash,
                        booking_data_hash = new_values.booking_data_hash,
                        updated_at = NOW()
                    """, (self.pro2_system_id, period.start_date, period.end_date, 
                          period.period_type, period.check_frequency, check_time, job_hash, booking_hash))
                    
                    await connection.commit()
                    
        except Exception as e:
            logger.error(f"更新最后检查时间失败: {e}")
            
    async def needs_check(self, period: AnalysisPeriod, current_dt: datetime) -> bool:
        """检查周期是否需要重新检查"""
        last_check = await self.get_last_check_time(period)
        if last_check is None:
            return True

        time_diff = current_dt - last_check

        if period.check_frequency == "3months":
            return time_diff.days >= 90
        elif period.check_frequency == "1month":
            return time_diff.days >= 30
        elif period.check_frequency == "1week":
            return time_diff.days >= 7
        elif period.check_frequency == "1day":
            return time_diff.days >= 1

        return False
        
    async def get_session_id(self) -> str:
        """生成唯一的会话ID（限制在32字符内）"""
        timestamp = int(datetime.now().timestamp())
        random_str = str(uuid.uuid4())[:8]
        # 格式: p{timestamp}{random} - 确保在32字符内
        return f"p{timestamp}{random_str}"
    
    def clean_data_for_mysql(self, value):
        """清理数据中的NaN值，防止MySQL错误"""
        if is_nan_value(value):
            return None
        return value
        
    async def calculate_data_hash(self, data: List[Dict[str, Any]], data_type: str) -> str:
        """计算数据的哈希值，用于变更检测，只使用核心业务字段"""
        if not data:
            return "empty_data"

        # 定义核心业务字段用于哈希计算（只包含真正的业务数据，排除所有可变字段）
        if data_type == "job":
            key_fields = [
                'job_date', 'job_file_no', 'income', 'cost', 'profit',
                'vessel', 'voyage', 'pol_code', 'pod_code', 'total_teu', 'total_cbm', 'total_rt',
                'business_type_name', 'bk_count'
                # 排除：operator_name, operator_department, job_handling_agent（可能变化）
            ]
        else:  # booking
            key_fields = [
                'job_date', 'job_file_no', 'business_no', 'income', 'cost', 'profit',
                'vessel', 'voyage', 'pol_code', 'pod_code', 'service_mode',
                'business_type_name', 'shipper_name'
                # 排除：salesman_name, operator_name, coloader_name（可能变化）
            ]
        
        # 构建标准化的数据用于哈希
        normalized_data = []
        for row in data:
            normalized_row = {}
            for field in key_fields:
                # 获取字段值，支持中文和英文字段名
                value = row.get(field)
                if value is None:
                    # 尝试中文字段名映射
                    cn_mapping = {
                        'job_date': '工作档日期',
                        'job_no': '工作档号', 
                        'income': '收入',
                        'cost': '成本',
                        'profit': '毛利润',
                        'operator_name': '操作',
                        'vessel': '船名',
                        'voyage': '航次',
                        'pol_code': '起运地',
                        'pod_code': '卸货地',
                        'job_type_cn': '业务类型中文',
                        'salesman_name': '业务员',
                        'bill_pol': '提单起运地',
                        'bill_pod': '提单卸货地',
                        'service_mode': '服务模式',
                        'client_name': '客户'
                    }
                    value = row.get(cn_mapping.get(field, field))
                
                # 标准化值
                if value is None:
                    normalized_row[field] = ""
                elif isinstance(value, (int, float, Decimal)):
                    normalized_row[field] = str(value)
                else:
                    normalized_row[field] = str(value).strip()
            
            normalized_data.append(normalized_row)
        
        # 按业务关键字段排序确保一致性
        sort_key = 'job_date' if 'job_date' in key_fields else list(key_fields)[0]
        normalized_data.sort(key=lambda x: (x.get(sort_key, ''), x.get('job_no', '')))
        
        # 计算哈希
        data_str = json.dumps(normalized_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(data_str.encode('utf-8')).hexdigest()
        
    async def get_existing_data_hash(self, period: AnalysisPeriod, data_type: str) -> Optional[str]:
        """从检查日志表获取上次分析的数据哈希值"""
        hash_field = "job_data_hash" if data_type == "job" else "booking_data_hash"
        
        try:
            async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(f"""
                        SELECT {hash_field} 
                        FROM t_scheduler_check_log 
                        WHERE pro2_system_id = %s 
                        AND period_start = %s 
                        AND period_end = %s 
                        AND period_type = %s
                        AND {hash_field} IS NOT NULL
                        ORDER BY last_check_time DESC 
                        LIMIT 1
                    """, (self.pro2_system_id, period.start_date, period.end_date, period.period_type))
                    
                    result = await cursor.fetchone()
                    return result[hash_field] if result else None
                    
        except Exception as e:
            logger.error(f"获取现有数据哈希失败: {e}")
            return None
    
    async def get_changed_records(self, new_data: List[Dict[str, Any]], data_type: str, period: AnalysisPeriod) -> List[Dict[str, Any]]:
        """获取有变化的记录（基于记录级哈希比较）"""
        if not new_data:
            return []
            
        table_name = "t_job_details" if data_type == "job" else "t_booking_details"
        changed_records = []
        
        try:
            async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    for row in new_data:
                        # 计算新记录的哈希值
                        new_hash = await self.calculate_data_hash([row], data_type)
                        
                        # 通过job_no查找现有记录的最新哈希值
                        # job数据使用job_file_no字段，booking数据使用job_file_no或job_no字段
                        if data_type == "job":
                            job_no = row.get('job_file_no') or row.get('工作档号')
                        else:
                            job_no = row.get('job_file_no') or row.get('job_no') or row.get('工作档号')
                        if not job_no:
                            # 如果没有job_no，直接加入变化列表
                            changed_records.append(row)
                            continue
                        
                        await cursor.execute(f"""
                            SELECT data_hash 
                            FROM {table_name} 
                            WHERE job_no = %s 
                            AND pro2_system_id = %s 
                            AND job_date BETWEEN %s AND %s
                            ORDER BY analysis_timestamp DESC 
                            LIMIT 1
                        """, (job_no, self.pro2_system_id, period.start_date, period.end_date))
                        
                        result = await cursor.fetchone()
                        existing_hash = result['data_hash'] if result else None
                        
                        # 如果哈希值不同或不存在，则认为是变化的记录
                        if existing_hash != new_hash:
                            changed_records.append(row)
                            logger.info(f"检测到变化记录 {job_no}: {existing_hash} -> {new_hash}")
                        else:
                            logger.debug(f"记录无变化 {job_no}: {existing_hash}")
                    
        except Exception as e:
            logger.error(f"检查记录变化失败: {e}")
            # 发生错误时返回所有记录以确保数据不丢失
            return new_data
            
        logger.info(f"{data_type}数据：总记录 {len(new_data)} 条，变化记录 {len(changed_records)} 条")
        return changed_records
                
    async def save_job_details(self, data: List[Dict[str, Any]], session_id: str, analysis_timestamp: int):
        """保存Job详情数据到t_job_details表"""
        if not data:
            logger.warning("Job数据为空，跳过保存")
            return
            
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor() as cursor:
                insert_sql = """
                INSERT INTO t_job_details (
                    session_id, analysis_timestamp, created_at, pro2_system_id,
                    job_type_cn, job_date, job_no, vessel, voyage, pol_code, pod_code,
                    bk_count, cbm, rt, teu, income, cost, profit,
                    transhipment_profit, total_business_profit,
                    operator_name, job_handling_agent, nomi_count, nomi_rt,
                    is_consolidation, bill_count, consolidation_20, consolidation_40,
                    operator_department, is_op_finished, is_checked,
                    job_id, job_type_id, operator_id, etd_date, eta_date, data_hash
                ) VALUES (
                    %s, %s, NOW(), %s,
                    %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s,
                    %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s,
                    %s, %s, %s, %s, %s, %s
                )
                """
                
                batch_data = []
                for row in data:
                    # 计算单条记录的哈希值
                    row_hash = await self.calculate_data_hash([row], "job")
                    
                    # 修正Job字段映射，确保所有必需字段都有正确的映射关系
                    # 优先使用英文字段名，中文字段名作为备选
                    job_type_cn = row.get('business_type_name') or row.get('business_type') or row.get('job_type_cn') or row.get('业务类型中文')
                    job_date = row.get('job_date') or row.get('工作档日期')
                    job_no = row.get('job_file_no') or row.get('job_no') or row.get('工作档号')
                    vessel = row.get('vessel') or row.get('船名')
                    voyage = row.get('voyage') or row.get('航次')
                    pol_code = row.get('pol_code') or row.get('起运地')
                    pod_code = row.get('pod_code') or row.get('卸货地')
                    
                    # 确保数值字段不为空或零，并进行数据清理和类型转换
                    def safe_numeric_convert(value, default=0, decimal_places=None):
                        """安全的数值转换，处理None、空字符串、NaN、无穷大等情况，支持DECIMAL精度控制"""
                        if value is None or value == '' or is_nan_value(value):
                            return default
                        try:
                            # 尝试转换为float
                            float_val = float(value) if value != 0 else default
                            # 检查是否为无穷大或NaN
                            if not (float_val == float_val) or float_val == float('inf') or float_val == float('-inf'):
                                return default
                            # 如果指定了小数位数，进行四舍五入
                            if decimal_places is not None:
                                float_val = round(float_val, decimal_places)
                            return float_val
                        except (ValueError, TypeError):
                            return default

                    bk_count = int(safe_numeric_convert(row.get('bk_count') or row.get('订舱数'), 0))
                    cbm = safe_numeric_convert(row.get('total_cbm') or row.get('cbm_sum') or row.get('cbm') or row.get('CBM总计'), 0, 3)  # DECIMAL(10,3)
                    rt = safe_numeric_convert(row.get('total_rt') or row.get('rt_sum') or row.get('rt') or row.get('RT总计'), 0, 3)  # DECIMAL(10,3)
                    teu = safe_numeric_convert(row.get('total_teu') or row.get('teu_sum') or row.get('teu') or row.get('TEU总计'), 0, 3)  # DECIMAL(10,3)

                    # 修正利润相关字段，使用安全转换，保持2位小数精度
                    income = safe_numeric_convert(row.get('income') or row.get('收入'), 0, 2)  # DECIMAL(15,2)
                    cost = safe_numeric_convert(row.get('cost') or row.get('成本'), 0, 2)  # DECIMAL(15,2)
                    profit = safe_numeric_convert(row.get('profit') or row.get('job_profit') or row.get('毛利润'), 0, 2)  # DECIMAL(15,2)

                    # 添加转运利润字段（新增）
                    transhipment_profit = safe_numeric_convert(row.get('transhipment_profit') or row.get('转运利润'), 0, 2)  # DECIMAL(15,2)
                    total_business_profit = safe_numeric_convert(row.get('total_business_profit') or row.get('业务总利润'), profit + transhipment_profit if profit and transhipment_profit else (profit or transhipment_profit or 0), 2)  # DECIMAL(15,2)
                    
                    operator_name = row.get('operator_name') or row.get('操作')
                    job_handling_agent = row.get('job_handling_agent_name') or row.get('job_handling_agent') or row.get('工作档代理')
                    
                    # 修正指定货相关字段
                    nomi_count = int(safe_numeric_convert(row.get('all_nominated_count') or row.get('nomi_count') or row.get('指定货票数'), 0))
                    nomi_rt = safe_numeric_convert(row.get('all_nominated_rt') or row.get('nomi_rt_count') or row.get('nomi_rt') or row.get('指定货RT总和'), 0, 3)  # DECIMAL(10,3)

                    # 修正集拼相关字段
                    bill_count = int(safe_numeric_convert(row.get('bl_count') or row.get('bill_count') or row.get('提单数量'), 0))
                    consolidation_20 = int(safe_numeric_convert(row.get('consol_20_count') or row.get('consol_20_sum') or row.get('consolidation_20') or row.get('集拼20尺柜数量'), 0))
                    consolidation_40 = int(safe_numeric_convert(row.get('consol_40_count') or row.get('consol_40_sum') or row.get('consolidation_40') or row.get('集拼40尺柜数量'), 0))
                    
                    # 集拼判断：根据 is_consol 字段或集拼票数判断
                    is_consol_flag = row.get('is_consol')
                    if is_consol_flag is not None:
                        is_consolidation = 1 if (is_consol_flag == 1 or is_consol_flag == '是' or str(is_consol_flag).lower() == 'true') else 0
                    else:
                        # 如果没有 is_consol 字段，根据集拼票数判断
                        consol_bk_count = safe_numeric_convert(row.get('consol_bk_count') or row.get('集拼订舱数量'), 0)
                        is_consolidation = 1 if consol_bk_count >= 3 else 0
                    
                    operator_department = row.get('operator_dept_name') or row.get('operator_department') or row.get('操作部门')
                    
                    # 修正状态字段
                    is_op_finished_value = row.get('is_op_finished')
                    is_op_finished = 1 if (is_op_finished_value == 1 or is_op_finished_value is True or is_op_finished_value == '是') else 0
                    
                    is_checked_value = row.get('is_checked')
                    is_checked = 1 if (is_checked_value == 1 or is_checked_value is True or is_checked_value == '是') else 0
                    
                    job_id = row.get('job_file_id') or row.get('job_id')
                    job_type_id = row.get('type_id') or row.get('job_type_id')
                    operator_id = row.get('operator_id')
                    etd_date = row.get('etd_date') or row.get('ETD')
                    eta_date = row.get('eta_date') or row.get('ETA')
                    
                    batch_data.append((
                        session_id, analysis_timestamp, self.pro2_system_id,
                        self.clean_data_for_mysql(job_type_cn),
                        self.clean_data_for_mysql(job_date),
                        self.clean_data_for_mysql(job_no),
                        self.clean_data_for_mysql(vessel),
                        self.clean_data_for_mysql(voyage),
                        self.clean_data_for_mysql(pol_code),
                        self.clean_data_for_mysql(pod_code),
                        self.clean_data_for_mysql(bk_count),
                        self.clean_data_for_mysql(cbm),
                        self.clean_data_for_mysql(rt),
                        self.clean_data_for_mysql(teu),
                        self.clean_data_for_mysql(income),
                        self.clean_data_for_mysql(cost),
                        self.clean_data_for_mysql(profit),
                        self.clean_data_for_mysql(transhipment_profit),
                        self.clean_data_for_mysql(total_business_profit),
                        self.clean_data_for_mysql(operator_name),
                        self.clean_data_for_mysql(job_handling_agent),
                        self.clean_data_for_mysql(nomi_count),
                        self.clean_data_for_mysql(nomi_rt),
                        is_consolidation,
                        self.clean_data_for_mysql(bill_count),
                        self.clean_data_for_mysql(consolidation_20),
                        self.clean_data_for_mysql(consolidation_40),
                        self.clean_data_for_mysql(operator_department),
                        is_op_finished,
                        is_checked,
                        self.clean_data_for_mysql(job_id),
                        self.clean_data_for_mysql(job_type_id),
                        self.clean_data_for_mysql(operator_id),
                        self.clean_data_for_mysql(etd_date),
                        self.clean_data_for_mysql(eta_date),
                        row_hash  # 添加哈希值
                    ))
                
                await cursor.executemany(insert_sql, batch_data)
                await connection.commit()
                
                logger.info(f"已保存 {len(batch_data)} 条Job详情数据，会话ID: {session_id}，系统ID: {self.pro2_system_id}")
    
    async def save_booking_details(self, data: List[Dict[str, Any]], session_id: str, analysis_timestamp: int):
        """保存Booking详情数据到t_booking_details表"""
        if not data:
            logger.warning("Booking数据为空，跳过保存")
            return
            
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor() as cursor:
                insert_sql = """
                INSERT INTO t_booking_details (
                    session_id, analysis_timestamp, created_at, pro2_system_id,
                    job_type_cn, job_date, job_no, bkbl_no, client_name,
                    vessel, voyage, job_pol, bill_pol, bill_pod,
                    service_mode, lcl_rt, teu, air_weight, income, cost, profit,
                    transhipment_profit, total_business_profit, is_freehand,
                    salesman_name, salesman_department, salesman_id,
                    operator_name, operator_department,
                    coloader_name, job_handling_agent, bl_handling_agent, is_transhipment, transhipment_id,
                    bkbl_id, job_id, job_type_id, operator_id, data_hash
                ) VALUES (
                    %s, %s, NOW(), %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s,
                    %s, %s, %s,
                    %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s
                )
                """
                
                batch_data = []
                for row in data:
                    # 计算单条记录的哈希值
                    row_hash = await self.calculate_data_hash([row], "booking")
                    
                    # 修正字段映射，确保所有必需字段都有正确的映射关系
                    # 优先使用英文字段名，中文字段名作为备选
                    job_type_cn = row.get('business_type_name') or row.get('job_type_cn') or row.get('业务类型中文')
                    job_date = row.get('job_date') or row.get('工作档日期')
                    job_no = row.get('job_file_no') or row.get('job_no') or row.get('工作档号')
                    bkbl_no = row.get('business_no') or row.get('bkbl_no') or row.get('订舱/提单号')
                    client_name = row.get('shipper_name') or row.get('client_name') or row.get('客户')
                    vessel = row.get('vessel') or row.get('船名')
                    voyage = row.get('voyage') or row.get('航次')
                    
                    # 修正港口字段映射
                    job_pol = row.get('sailing_pol') or row.get('job_pol') or row.get('航次始发港')
                    bill_pol = row.get('pol_code') or row.get('bkbl_pol') or row.get('bill_pol') or row.get('提单起运地')
                    bill_pod = row.get('pod_code') or row.get('bkbl_pod') or row.get('bill_pod') or row.get('提单卸货地')
                    
                    service_mode = row.get('service_mode') or row.get('服务模式')
                    
                    # 修正重量和TEU字段映射 - 根据service_mode决定使用哪个字段，增强数据清理
                    def safe_numeric_convert_booking(value, default=0, decimal_places=None):
                        """安全的数值转换，处理None、空字符串、NaN、无穷大等情况，支持DECIMAL精度控制"""
                        if value is None or value == '' or is_nan_value(value):
                            return default
                        try:
                            # 尝试转换为float
                            float_val = float(value) if value != 0 else default
                            # 检查是否为无穷大或NaN
                            if not (float_val == float_val) or float_val == float('inf') or float_val == float('-inf'):
                                return default
                            # 如果指定了小数位数，进行四舍五入
                            if decimal_places is not None:
                                float_val = round(float_val, decimal_places)
                            return float_val
                        except (ValueError, TypeError):
                            return default

                    rt_value = safe_numeric_convert_booking(row.get('lcl_rt') or row.get('rt'), 0, 3)
                    
                    # 根据服务模式映射字段
                    if service_mode == 'LCL':
                        lcl_rt = rt_value
                        teu = 0  # LCL不计TEU
                        air_weight = None
                    elif service_mode == 'FCL':
                        lcl_rt = None
                        # TEU = 计费数量/1000，参考data_format_converter.py的正确逻辑
                        teu_value = safe_numeric_convert_booking(row.get('teu'), 0, 3)
                        teu = teu_value if teu_value > 0 else (round(rt_value / 1000, 3) if rt_value > 0 else 0)
                        air_weight = None
                    elif service_mode == 'AIR':
                        lcl_rt = None
                        teu = 0  # 空运不计TEU
                        air_weight = safe_numeric_convert_booking(row.get('air_weight'), rt_value, 3)
                    else:
                        lcl_rt = rt_value if rt_value > 0 else None
                        teu = safe_numeric_convert_booking(row.get('teu'), 0, 3)
                        air_weight = safe_numeric_convert_booking(row.get('air_weight'), None, 3)

                    income = safe_numeric_convert_booking(row.get('income') or row.get('收入'), 0, 2)  # DECIMAL(15,2)
                    cost = safe_numeric_convert_booking(row.get('cost') or row.get('成本'), 0, 2)  # DECIMAL(15,2)
                    profit = safe_numeric_convert_booking(row.get('profit') or row.get('利润'), 0, 2)  # DECIMAL(15,2)
                    transhipment_profit = safe_numeric_convert_booking(row.get('transhipment_profit') or row.get('转运利润'), 0, 2)  # DECIMAL(15,2)
                    total_business_profit = safe_numeric_convert_booking((profit + transhipment_profit) if profit is not None and transhipment_profit is not None else (profit or transhipment_profit or 0), 0, 2)  # DECIMAL(15,2)
                    
                    # 修正业务员字段映射
                    is_freehand_value = row.get('is_free_hand')
                    is_freehand = 1 if (is_freehand_value == 1 or is_freehand_value == '是' or str(is_freehand_value).lower() == 'true') else 0
                    salesman_name = row.get('salesman_name') or row.get('salesman') or row.get('业务员')
                    salesman_department = row.get('salesman_dept_name') or row.get('salesman_department') or row.get('业务员部门')
                    salesman_id = row.get('salesman_id') or row.get('salesman_dept_id')
                    
                    operator_name = row.get('operator_name') or row.get('操作')
                    operator_department = row.get('operator_dept_name') or row.get('operator_department') or row.get('操作部门')

                    # 修复操作员信息为空的问题：如果operator_name或operator_department为空但operator_id存在，尝试通过ID获取
                    operator_id = row.get('operator_id')
                    if operator_id and (not operator_name or not operator_department):
                        try:
                            # 通过operator_id直接查询数据库获取操作员信息
                            from utils.basic.data_conn_unified import execute_pro2_query
                            sql = """
                                SELECT u.full_name, ud.name as dept_name
                                FROM users u
                                LEFT JOIN users_department ud ON u.dept_id = ud.dept_id
                                WHERE u.user_id = ? AND u.is_active = 1
                            """
                            result = execute_pro2_query(sql, (operator_id,), fetch_all=True)
                            if result and len(result) > 0:
                                if not operator_name and result[0][0]:
                                    operator_name = result[0][0]  # full_name
                                if not operator_department and result[0][1]:
                                    operator_department = result[0][1]  # dept_name
                                logger.debug(f"通过operator_id={operator_id}补充操作员信息: {operator_name}, {operator_department}")
                        except Exception as e:
                            logger.debug(f"通过operator_id={operator_id}获取操作员信息失败: {e}")
                    
                    coloader_name = row.get('coloader_name') or row.get('coloader')
                    job_handling_agent = row.get('job_handling_agent_name') or row.get('job_handling_agent') or row.get('工作档代理')
                    bl_handling_agent = row.get('bl_handling_agent_name') or row.get('bl_handling_agent') or row.get('操作代理')
                    
                    is_transhipment_value = row.get('is_transhipment')
                    is_transhipment = 1 if (is_transhipment_value == 1 or is_transhipment_value == '是' or str(is_transhipment_value).lower() == 'true') else 0
                    transhipment_id = row.get('transhipment_id') or row.get('转运ID')
                    
                    bkbl_id = row.get('bkbl_id')
                    job_id = row.get('job_file_id') or row.get('job_id')
                    job_type_id = row.get('type_id') or row.get('job_type_id')
                    operator_id = row.get('operator_id')
                    
                    batch_data.append((
                        session_id, analysis_timestamp, self.pro2_system_id,
                        self.clean_data_for_mysql(job_type_cn),
                        self.clean_data_for_mysql(job_date),
                        self.clean_data_for_mysql(job_no),
                        self.clean_data_for_mysql(bkbl_no),
                        self.clean_data_for_mysql(client_name),
                        self.clean_data_for_mysql(vessel),
                        self.clean_data_for_mysql(voyage),
                        self.clean_data_for_mysql(job_pol),
                        self.clean_data_for_mysql(bill_pol),
                        self.clean_data_for_mysql(bill_pod),
                        self.clean_data_for_mysql(service_mode),
                        self.clean_data_for_mysql(lcl_rt),
                        self.clean_data_for_mysql(teu),
                        self.clean_data_for_mysql(air_weight),
                        self.clean_data_for_mysql(income),
                        self.clean_data_for_mysql(cost),
                        self.clean_data_for_mysql(profit),
                        self.clean_data_for_mysql(transhipment_profit),
                        self.clean_data_for_mysql(total_business_profit),
                        is_freehand,
                        self.clean_data_for_mysql(salesman_name),
                        self.clean_data_for_mysql(salesman_department),
                        self.clean_data_for_mysql(salesman_id),
                        self.clean_data_for_mysql(operator_name),
                        self.clean_data_for_mysql(operator_department),
                        self.clean_data_for_mysql(coloader_name),
                        self.clean_data_for_mysql(job_handling_agent),
                        self.clean_data_for_mysql(bl_handling_agent),
                        is_transhipment,
                        self.clean_data_for_mysql(transhipment_id),
                        self.clean_data_for_mysql(bkbl_id),
                        self.clean_data_for_mysql(job_id),
                        self.clean_data_for_mysql(job_type_id),
                        self.clean_data_for_mysql(operator_id),
                        row_hash  # 添加哈希值
                    ))
                
                await cursor.executemany(insert_sql, batch_data)
                await connection.commit()
                
                logger.info(f"已保存 {len(batch_data)} 条Booking详情数据，会话ID: {session_id}，系统ID: {self.pro2_system_id}")
    
    async def process_period(self, period: AnalysisPeriod) -> bool:
        """处理单个分析周期"""
        try:
            logger.info(f"开始处理周期: {period.start_date} 至 {period.end_date} ({period.period_type})")
            
            session_id = await self.get_session_id()
            analysis_timestamp = int(datetime.now().timestamp())
            
            # 获取新数据
            start_str = period.start_date.strftime('%Y-%m-%d')
            end_str = period.end_date.strftime('%Y-%m-%d')
            
            # 获取Job数据
            job_result = await get_job_details_with_transhipment(start_str, end_str)
            job_data = job_result.get('data', []) if isinstance(job_result, dict) else []
            logger.info(f"获取到Job数据: {len(job_data)} 条记录")
            
            # 使用记录级哈希比较
            changed_job_records = await self.get_changed_records(job_data, "job", period)
            if changed_job_records:
                logger.info(f"Job数据有变更，新增变化记录: {len(changed_job_records)} 条（总数据 {len(job_data)} 条）")
                await self.save_job_details(changed_job_records, session_id, analysis_timestamp)
            else:
                logger.info("Job数据无变更，跳过保存")
            
            # 获取Booking数据
            booking_result = await get_sea_air_profit_with_transhipment(start_str, end_str)
            booking_data = booking_result.get('data', []) if isinstance(booking_result, dict) else []
            
            # 使用记录级哈希比较
            changed_booking_records = await self.get_changed_records(booking_data, "booking", period)
            if changed_booking_records:
                logger.info(f"Booking数据有变更，新增变化记录: {len(changed_booking_records)} 条（总数据 {len(booking_data)} 条）")
                await self.save_booking_details(changed_booking_records, session_id, analysis_timestamp)
            else:
                logger.info("Booking数据无变更，跳过保存")
                
            # 计算整体哈希用于检查日志
            new_job_hash = await self.calculate_data_hash(job_data, "job") if job_data else "empty_data"
            new_booking_hash = await self.calculate_data_hash(booking_data, "booking") if booking_data else "empty_data"
            
            # 更新最后检查时间和哈希值到数据库
            await self.update_last_check_time(period, datetime.now(), new_job_hash, new_booking_hash)
            
            return True
            
        except Exception as e:
            logger.error(f"处理周期失败 {period.start_date}-{period.end_date}: {e}")
            return False
            
    async def process_periods_async(self, periods: List[AnalysisPeriod], current_dt: datetime) -> Tuple[int, int]:
        """异步处理多个周期，按频率分组并发执行，支持分层执行时间检查"""
        # 按检查频率分组
        groups = {
            "3months": [],
            "1month": [],
            "1week": [],
            "1day": []
        }

        # 筛选需要检查的周期并分组，同时检查执行时间
        for period in periods:
            # 首先检查是否需要检查（基于时间间隔）
            if await self.needs_check(period, current_dt):
                # 然后检查是否在允许的执行时间内（基于周期类型）
                if await self.should_run_analysis(current_dt, period.period_type):
                    groups[period.check_frequency].append(period)
                else:
                    logger.debug(f"周期 {period.period_type} ({period.start_date}~{period.end_date}) 不在执行时间窗口内，跳过")

        processed_count = 0
        failed_count = 0

        # 按优先级异步处理不同频率的分组（每天检查优先级最高）
        for frequency in ["1day", "1week", "1month", "3months"]:
            if groups[frequency]:
                logger.info(f"异步处理 {frequency} 频率的 {len(groups[frequency])} 个周期")

                # 创建异步任务列表
                tasks = [self.process_period(period) for period in groups[frequency]]

                # 并发执行，最多同时处理3个周期以避免数据库压力过大
                semaphore = asyncio.Semaphore(3)

                async def process_with_semaphore(task):
                    async with semaphore:
                        return await task

                # 执行所有任务
                results = await asyncio.gather(
                    *[process_with_semaphore(task) for task in tasks],
                    return_exceptions=True
                )

                # 统计结果
                for result in results:
                    if isinstance(result, Exception):
                        logger.error(f"异步处理异常: {result}")
                        failed_count += 1
                    elif result:
                        processed_count += 1
                    else:
                        failed_count += 1

                # 每组之间稍作休息
                await asyncio.sleep(1)

        return processed_count, failed_count
    
    async def run_scheduled_analysis(self):
        """运行计划的分析任务（异步优化版本）- 支持分层执行时间"""
        if self.is_running:
            logger.warning("分析任务正在运行中，跳过本次执行")
            return

        current_dt = datetime.now()

        # 不再进行全局时间检查，而是在process_periods_async中按周期类型检查
        # 这样可以支持不同周期类型的不同执行时间策略

        self.is_running = True
        logger.info("开始执行周期性利润数据分析（分层执行时间模式）")

        try:
            periods = self.get_analysis_periods()
            logger.info(f"总共生成 {len(periods)} 个分析周期")

            # 异步处理所有周期，内部会根据周期类型检查执行时间
            processed_count, failed_count = await self.process_periods_async(periods, current_dt)

            logger.info(f"周期性分析完成：处理 {processed_count} 个周期，失败 {failed_count} 个")

        except Exception as e:
            logger.error(f"周期性分析异常: {e}")
        finally:
            self.is_running = False
            
    async def start_scheduler(self):
        """启动调度器"""
        logger.info("利润数据调度器启动")
        
        # 确保必要的数据库表存在
        await self.ensure_required_tables_exist()
        
        while True:
            try:
                await self.run_scheduled_analysis()
                # 每小时检查一次
                await asyncio.sleep(3600)
            except Exception as e:
                logger.error(f"调度器异常: {e}")
                await asyncio.sleep(300)  # 错误时5分钟后重试