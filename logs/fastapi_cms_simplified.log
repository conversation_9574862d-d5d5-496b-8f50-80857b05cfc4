2025-07-10 23:10:36 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:10:57 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDatalake，包含t_booking_details和t_job_details表
2025-07-10 23:10:57 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:10:57 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:11:23 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:11:39 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDatalake，包含t_booking_details和t_job_details表
2025-07-10 23:11:39 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:11:39 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:12:16 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:12:48 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDatalake，包含t_booking_details和t_job_details表
2025-07-10 23:12:48 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:12:48 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:16:27 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:17:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDatalake，包含t_booking_details和t_job_details表
2025-07-10 23:17:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:17:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:21:04 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:21:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:21:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:21:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:22:15 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:22:15 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - PandasAI 分析失败: 'Engine' object has no attribute 'cursor'
2025-07-10 23:22:15 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - 错误堆栈: Traceback (most recent call last):
  File "/Volumes/PSSD/code_files/mcp-cms/utils/fastapi_apps/fastapi_cms_simplified.py", line 153, in analyze_data_with_pandasai
    data_df = pd.read_sql(sql_query, engine)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 708, in read_sql
    return pandas_sql.read_query(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 2728, in read_query
    cursor = self.execute(sql, params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 2662, in execute
    cur = self.con.cursor()
          ^^^^^^^^^^^^^^^
AttributeError: 'Engine' object has no attribute 'cursor'

2025-07-10 23:22:39 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:22:55 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - PandasAI 分析失败: 'Connection' object has no attribute 'cursor'
2025-07-10 23:22:55 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - 错误堆栈: Traceback (most recent call last):
  File "/Volumes/PSSD/code_files/mcp-cms/utils/fastapi_apps/fastapi_cms_simplified.py", line 154, in analyze_data_with_pandasai
    data_df = pd.read_sql(sql_query, connection)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 708, in read_sql
    return pandas_sql.read_query(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 2728, in read_query
    cursor = self.execute(sql, params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 2662, in execute
    cur = self.con.cursor()
          ^^^^^^^^^^^^^^^
AttributeError: 'Connection' object has no attribute 'cursor'

2025-07-10 23:23:26 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:23:26 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - PandasAI 分析失败: cannot import name 'get_db_connection_mcp' from 'utils.basic.data_conn_unified' (/Volumes/PSSD/code_files/mcp-cms/utils/basic/data_conn_unified.py)
2025-07-10 23:23:26 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - 错误堆栈: Traceback (most recent call last):
  File "/Volumes/PSSD/code_files/mcp-cms/utils/fastapi_apps/fastapi_cms_simplified.py", line 140, in analyze_data_with_pandasai
    from utils.basic.data_conn_unified import get_db_connection_mcp
ImportError: cannot import name 'get_db_connection_mcp' from 'utils.basic.data_conn_unified' (/Volumes/PSSD/code_files/mcp-cms/utils/basic/data_conn_unified.py)

2025-07-10 23:24:32 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:24:50 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:24:50 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:24:50 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:25:16 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:25:40 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:25:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:25:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:25:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:26:24 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:26:52 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:27:10 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:27:10 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:27:10 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 数据总共有多少条记录？
2025-07-10 23:27:16 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:27:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:28:01 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:28:01 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:28:01 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析收入和成本的分布情况，计算平均利润率
2025-07-10 23:28:11 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:47:25 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:47:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了0条记录
2025-07-10 23:47:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:47:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析收入和成本的分布情况，计算平均利润率
2025-07-10 23:47:50 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:49:02 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:49:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:49:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:49:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 数据库中有哪些不同的pro2_system_id值？每个值有多少条记录？
2025-07-10 23:49:48 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
