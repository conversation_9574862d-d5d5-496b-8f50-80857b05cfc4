# PandasAI MySQL数据源集成文档

## 概述

本文档描述了将PandasAI从使用传入DataFrame数据改为直接连接MySQL数据库进行分析的修改。

## 主要修改

### 1. 函数签名变更

**修改前：**
```python
async def analyze_data_with_pandasai(data: List[Dict], question: str, language: str = "zh") -> str:
```

**修改后：**
```python
async def analyze_data_with_pandasai(
    question: str, 
    language: str = "zh", 
    session_id: str = None,
    begin_date: str = None,
    end_date: str = None,
    data_type: str = "booking"
) -> str:
```

### 2. 数据库连接

- 使用项目现有的`connect_mysql`函数连接到`mcp_tokens`数据库
- 支持连接`t_booking_details`和`t_job_details`两个表
- 根据`data_type`参数自动选择对应的表

### 3. 数据去重逻辑

在分析提示中包含了数据去重说明：
- 如果存在相同的job_id或job_id+bkbl_id的记录，选择id字段值更大的记录作为最终数据
- PandasAI能够理解并应用这个去重逻辑

### 4. 配置优化

```python
df = SmartDataframe(
    data_df,
    config={
        "llm": llm,
        "enable_cache": False,
        "verbose": True,
        "enforce_privacy": False,  # 关闭隐私保护以允许导入模块
        "save_charts": False,
        "open_charts": False,
        "custom_whitelisted_dependencies": ["pandas", "numpy", "matplotlib", "seaborn"],
    },
)
```

## 使用示例

### 基本调用

```python
result = await analyze_data_with_pandasai(
    question="分析收入和成本的分布情况，计算平均利润率",
    language="zh",
    session_id=None,
    begin_date="2025-06-10",
    end_date="2025-07-10",
    data_type="booking"  # 或 "job"
)
```

### 在FastAPI端点中的调用

```python
analysis_result = await asyncio.wait_for(
    analyze_data_with_pandasai(
        question=request.question,
        language=request.language,
        session_id=None,
        begin_date=request.begin_date,
        end_date=request.end_date,
        data_type=request.data_type
    ),
    timeout=600.0  # 10分钟超时
)
```

## 测试结果

### 测试环境
- 数据库：`qd.cmsgroup.com.cn:33306/mcp_tokens`
- 测试表：`t_booking_details`
- 测试数据：4464条记录

### 测试案例

1. **简单查询测试**
   - 问题：数据总共有多少条记录？
   - 结果：✅ 成功返回记录数和去重分析

2. **复杂分析测试**
   - 问题：分析收入和成本的分布情况，计算平均利润率
   - 结果：✅ 成功生成包含数据概览、关键发现、趋势分析和建议的完整报告

## 技术特点

1. **直接数据库连接**：不再需要预先获取数据，直接连接MySQL进行分析
2. **智能表选择**：根据data_type自动选择对应的表
3. **数据去重支持**：AI能理解并应用复杂的去重逻辑
4. **多语言支持**：支持中文和英文分析报告
5. **日期过滤**：支持按日期范围过滤数据
6. **错误处理**：完善的错误处理和日志记录

## 依赖项

新增的依赖项：
- `pandasai[connectors]`：PandasAI连接器支持
- `sqlalchemy`：数据库连接支持
- `pymysql`：MySQL驱动

## 注意事项

1. **字体警告**：在生成中文图表时可能出现字体缺失警告，不影响功能
2. **数据去重**：去重逻辑在AI分析提示中说明，依赖AI理解执行
3. **性能考虑**：大数据量时建议使用日期过滤减少数据量
4. **连接管理**：使用完毕后自动关闭数据库连接

## 文件位置

- 主要修改文件：`utils/fastapi_apps/fastapi_cms_simplified.py`
- 测试文件：`test_mysql_pandasai.py`
- 文档文件：`docs/pandasai_mysql_integration.md`
